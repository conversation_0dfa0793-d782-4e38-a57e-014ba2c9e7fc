"""应用主入口"""

import logging

import uvicorn

from app.db.init_db import init_db, init_test_data
from app.db.session import SessionLocal

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init() -> None:
    """初始化应用"""
    # 初始化数据库表结构
    from app.db.session import Base, engine
    Base.metadata.create_all(bind=engine)
    logger.info("数据库表已创建")
    
    # 初始化权限和测试数据
    db = SessionLocal()
    try:
        # 初始化权限数据
        init_db(db)
    finally:
        db.close()

    # 初始化测试数据
    db = SessionLocal()
    try:
        init_test_data(db)
        logger.info("测试数据初始化完成")
    finally:
        db.close()


def main() -> None:
    """应用主入口"""
    logger.info("初始化应用...")
    init()
    logger.info("初始化完成")

    logger.info("启动应用服务器...")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )


if __name__ == "__main__":
    main()
