<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信扫码登录演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .qr-container {
            text-align: center;
            margin: 20px 0;
        }
        .qr-code {
            border: 1px solid #ddd;
            padding: 20px;
            display: inline-block;
            background: white;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .status.waiting {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .status.scanned {
            background-color: #fff3e0;
            color: #f57c00;
        }
        .status.confirmed {
            background-color: #e8f5e8;
            color: #388e3c;
        }
        .status.expired {
            background-color: #ffebee;
            color: #d32f2f;
        }
        .status.need_register {
            background-color: #fff3e0;
            color: #f57c00;
        }
        .user-info {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .register-form {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .register-form input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .register-form button {
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        .register-form button:hover {
            background: #45a049;
        }
        button {
            background: #2196F3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信公众号扫码登录演示</h1>
        
        <div id="qr-section">
            <button onclick="createQRCode()">生成登录二维码</button>
            
            <div id="qr-container" class="qr-container hidden">
                <h3>请使用微信扫描二维码登录</h3>
                <div class="qr-code">
                    <img id="qr-image" src="" alt="二维码" style="max-width: 200px;">
                </div>
                <p>二维码有效期：10分钟</p>
            </div>
        </div>

        <div id="status-container" class="hidden">
            <div id="status" class="status waiting">
                等待扫码...
            </div>
        </div>

        <div id="user-info" class="user-info hidden">
            <h3>用户信息</h3>
            <div id="user-details"></div>
        </div>

        <div id="register-section" class="register-form hidden">
            <h3>完成注册</h3>
            <p>检测到您是新用户，请完成注册：</p>
            <input type="text" id="username" placeholder="请输入用户名" required>
            <input type="text" id="nickname" placeholder="请输入昵称（可选）">
            <button onclick="registerUser()">完成注册</button>
        </div>

        <div id="login-success" class="hidden">
            <h3>登录成功！</h3>
            <p>访问令牌：<span id="access-token"></span></p>
        </div>
    </div>

    <script>
        let currentSceneStr = null;
        let statusCheckInterval = null;
        let currentOpenId = null;

        async function createQRCode() {
            try {
                const response = await fetch('/wechat/qr-login/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error('创建二维码失败');
                }
                
                const data = await response.json();
                currentSceneStr = data.scene_str;
                
                // 显示二维码
                document.getElementById('qr-image').src = data.qr_url;
                document.getElementById('qr-container').classList.remove('hidden');
                document.getElementById('status-container').classList.remove('hidden');
                
                // 开始轮询状态
                startStatusCheck();
                
            } catch (error) {
                alert('创建二维码失败: ' + error.message);
            }
        }

        function startStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
            
            statusCheckInterval = setInterval(checkLoginStatus, 2000);
        }

        async function checkLoginStatus() {
            if (!currentSceneStr) return;
            
            try {
                const response = await fetch(`/wechat/qr-login/status/${currentSceneStr}`);
                const data = await response.json();
                
                updateStatus(data);
                
                if (data.status === 'confirmed' && data.access_token) {
                    // 登录成功
                    clearInterval(statusCheckInterval);
                    showLoginSuccess(data);
                } else if (data.status === 'need_register') {
                    // 需要注册
                    clearInterval(statusCheckInterval);
                    showRegisterForm(data);
                } else if (data.status === 'expired') {
                    // 二维码过期
                    clearInterval(statusCheckInterval);
                }
                
            } catch (error) {
                console.error('检查登录状态失败:', error);
            }
        }

        function updateStatus(data) {
            const statusElement = document.getElementById('status');
            const statusMessages = {
                'waiting': '等待扫码...',
                'scanned': '已扫码，请在手机上确认登录',
                'confirmed': '登录确认中...',
                'expired': '二维码已过期，请重新生成',
                'need_register': '检测到新用户，需要注册'
            };
            
            statusElement.textContent = statusMessages[data.status] || data.message || '未知状态';
            statusElement.className = `status ${data.status}`;
            
            if (data.user_info) {
                showUserInfo(data.user_info);
                currentOpenId = data.user_info.openid;
            }
        }

        function showUserInfo(userInfo) {
            const userInfoElement = document.getElementById('user-info');
            const userDetailsElement = document.getElementById('user-details');
            
            userDetailsElement.innerHTML = `
                <p><strong>微信昵称：</strong>${userInfo.nickname || '未知'}</p>
                <p><strong>性别：</strong>${userInfo.sex === 1 ? '男' : userInfo.sex === 2 ? '女' : '未知'}</p>
                <p><strong>地区：</strong>${userInfo.country || ''} ${userInfo.province || ''} ${userInfo.city || ''}</p>
                ${userInfo.headimgurl ? `<img src="${userInfo.headimgurl}" alt="头像" style="width: 50px; height: 50px; border-radius: 25px;">` : ''}
            `;
            
            userInfoElement.classList.remove('hidden');
        }

        function showRegisterForm(data) {
            document.getElementById('register-section').classList.remove('hidden');
            if (data.user_info && data.user_info.nickname) {
                document.getElementById('nickname').value = data.user_info.nickname;
            }
        }

        async function registerUser() {
            const username = document.getElementById('username').value.trim();
            const nickname = document.getElementById('nickname').value.trim();
            
            if (!username) {
                alert('请输入用户名');
                return;
            }
            
            if (!currentOpenId) {
                alert('缺少微信信息，请重新扫码');
                return;
            }
            
            try {
                const response = await fetch('/wechat/register-with-wechat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        openid: currentOpenId,
                        username: username,
                        nickname: nickname
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '注册失败');
                }
                
                const data = await response.json();
                showLoginSuccess(data);
                
            } catch (error) {
                alert('注册失败: ' + error.message);
            }
        }

        function showLoginSuccess(data) {
            document.getElementById('register-section').classList.add('hidden');
            document.getElementById('qr-section').classList.add('hidden');
            document.getElementById('status-container').classList.add('hidden');
            
            document.getElementById('access-token').textContent = data.access_token;
            document.getElementById('login-success').classList.remove('hidden');
        }
    </script>
</body>
</html>
