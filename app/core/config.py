"""应用配置模块"""

from functools import lru_cache

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置设置"""

    # 应用设置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Steam游戏数据聚合API"

    # 数据库设置
    DATABASE_URL: str = Field(
        default="sqlite:///./steam_data.db", description="数据库连接URL"
    )

    # Redis设置
    REDIS_URL: str = Field(
        default="redis://localhost:6379/0", description="Redis连接URL"
    )

    # Celery设置
    CELERY_BROKER_URL: str = Field(
        default="redis://localhost:6379/1", description="Celery Broker URL"
    )
    CELERY_RESULT_BACKEND: str = Field(
        default="redis://localhost:6379/2", description="Celery Result Backend"
    )

    # 安全设置
    SECRET_KEY: str = Field(
        default="your-secret-key",  # 在生产环境中应该更改
        description="用于JWT令牌加密的密钥",
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 天

    # 阿里云访问密钥
    ALIBABA_CLOUD_ACCESS_KEY_ID: str = Field(
        default="your-access-key-id", description="阿里云访问密钥ID"
    )
    ALIBABA_CLOUD_ACCESS_KEY_SECRET: str = Field(
        default="your-access-key-secret", description="阿里云访问密钥"
    )

    # 阿里云短信服务
    TEMPLATE_CODE: str = Field(default="SMS_154950909", description="短信模板代码")
    SIGN_NAME: str = Field(default="阿里云短信服务", description="短信签名")

    # 微信公众号设置
    WECHAT_APP_ID: str = Field(
        default="your-wechat-app-id", description="微信公众号AppID"
    )
    WECHAT_APP_SECRET: str = Field(
        default="your-wechat-app-secret", description="微信公众号AppSecret"
    )

    # 微信公众号消息推送
    WECHAT_MESSAGE_TOKEN: str = Field(
        default="your-wechat-message-token", description="微信公众号消息推送Token"
    )
    WECHAT_MESSAGE_SECRET_KEY: str = Field(
        default="your-wechat-message-secret-key",
        description="微信公众号消息推送SecretKey",
    )

    # Steam API设置
    STEAM_API_KEY: str = Field(
        default="your-steam-api-key", description="Steam API密钥"
    )

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"  # 忽略额外的字段


# 创建设置实例
settings = Settings()


@lru_cache
def get_settings() -> Settings:
    return Settings()
