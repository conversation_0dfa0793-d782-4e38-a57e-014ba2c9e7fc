"""应用主入口"""

import json
from typing import Any

from fastapi import FastAP<PERSON>, Response
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request

from app.api.api import api_router

# 微信公众号消息推送api
from app.api.endpoints.wechat import router as wechat_router
from app.core.config import settings
from app.services.logger import logger


class ResponseFormatterMiddleware(BaseHTTPMiddleware):
    """重写全局响应格式化中间件"""

    async def dispatch(self, request: Request, call_next) -> Any:
        response = await call_next(request)
        # 对于 204 状态码，直接返回原始响应
        if response.status_code == 204:
            return None

        if (
            request.url.path.startswith("/api/v1")
            and response.status_code < 400
            and response.status_code != 204
            and request.url.path not in ["/api/v1/health"]
        ):
            try:
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                content = json.loads(body.decode("utf-8")) if body else {}
                formatted_content = {"status": "success", "data": content}
                response_body = json.dumps(formatted_content).encode("utf-8")
                response.headers["Content-Length"] = str(len(response_body))
                return Response(
                    content=response_body,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    media_type=response.media_type,
                )
            except Exception:
                return Response(
                    content="Internal Server Error",
                    status_code=500,
                    headers=dict(response.headers),
                    media_type=response.media_type,
                )
        return response


# 创建FastAPI应用实例
app = FastAPI(
    title="Steam数据聚合API",
    description="提供Steam数据的聚合和分析服务",
    version="0.1.0",
    # 生产默认关闭默认文档
    # openapi_url=f"{settings.API_V1_STR}/openapi.json",
)

# 初始化日志
logger.info("应用程序启动中...")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加响应格式化中间件
app.add_middleware(ResponseFormatterMiddleware)


@app.get("/")
async def root():
    """API根路径，返回欢迎信息"""
    return {"message": "欢迎使用Steam游戏数据聚合API"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy"}


# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)
app.include_router(wechat_router, prefix="/wechat")
