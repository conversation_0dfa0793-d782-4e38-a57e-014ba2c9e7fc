from datetime import datetime

from pydantic import BaseModel


class GameBase(BaseModel):
    """游戏基础模型"""
    name: str
    description: str | None = None
    developer: str | None = None
    publisher: str | None = None
    price: float | None = None
    discount_percent: int | None = None
    release_date: datetime | None = None
    image_url: str | None = None


class GameCreate(GameBase):
    """创建游戏的请求模型"""
    steam_appid: int


class GameUpdate(GameBase):
    """更新游戏的请求模型"""
    name: str | None = None
    steam_appid: int | None = None


class GameInDBBase(GameBase):
    """数据库中游戏的基础模型"""
    id: int
    steam_appid: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Game(GameInDBBase):
    """API响应中的游戏模型"""
    pass


class GameList(BaseModel):
    """游戏列表响应模型"""
    total: int
    items: list[Game]