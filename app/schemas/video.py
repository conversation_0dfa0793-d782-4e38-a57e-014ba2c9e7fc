from datetime import datetime

from pydantic import BaseModel, HttpUrl


class VideoBase(BaseModel):
    """视频基础模型"""
    title: str
    description: str
    url: HttpUrl
    cover_url: HttpUrl | None = None
    duration: int | None = None  # 视频时长（秒）
    is_published: bool = False


class VideoCreate(VideoBase):
    """创建视频的请求模型"""
    pass


class VideoUpdate(BaseModel):
    """更新视频的请求模型"""
    title: str | None = None
    description: str | None = None
    url: HttpUrl | None = None
    cover_url: HttpUrl | None = None
    duration: int | None = None
    is_published: bool | None = None


class VideoInDBBase(VideoBase):
    """数据库中视频的基础模型"""
    id: int
    author_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Video(VideoInDBBase):
    """API响应中的视频模型"""
    pass


class VideoList(BaseModel):
    """视频列表响应模型"""
    total: int
    items: list[Video]