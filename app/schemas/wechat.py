"""微信相关的Pydantic模型"""

from typing import Optional
from pydantic import BaseModel


class QRCodeResponse(BaseModel):
    """二维码响应模型"""
    scene_str: str
    qr_url: str
    expire_seconds: int


class LoginStatusResponse(BaseModel):
    """登录状态响应模型"""
    status: str  # waiting, scanned, confirmed, expired
    message: Optional[str] = None
    user_info: Optional[dict] = None
    access_token: Optional[str] = None
    token_type: Optional[str] = None


class WeChatUserInfo(BaseModel):
    """微信用户信息模型"""
    openid: str
    nickname: str
    sex: int
    province: str
    city: str
    country: str
    headimgurl: str
    privilege: list = []
    unionid: Optional[str] = None


class WeChatLoginRequest(BaseModel):
    """微信登录请求模型"""
    scene_str: str


class WeChatBindRequest(BaseModel):
    """微信绑定请求模型"""
    openid: str
    user_id: Optional[int] = None


class WeChatMessageEvent(BaseModel):
    """微信消息事件模型"""
    ToUserName: str
    FromUserName: str
    CreateTime: int
    MsgType: str
    Event: Optional[str] = None
    EventKey: Optional[str] = None
    Ticket: Optional[str] = None
