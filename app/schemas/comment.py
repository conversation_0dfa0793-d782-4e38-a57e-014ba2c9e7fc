from datetime import datetime

from pydantic import BaseModel, Field

from app.models.comment import CommentType


class CommentBase(BaseModel):
    """评论基础模型"""
    content: str = Field(..., description="评论内容")
    comment_type: CommentType = Field(..., description="评论类型：article或video")
    article_id: int | None = Field(None, description="文章ID，当comment_type为article时必填")
    video_id: int | None = Field(None, description="视频ID，当comment_type为video时必填")
    is_visible: bool | None = Field(True, description="是否可见")


class CommentCreate(CommentBase):
    """创建评论模型"""
    pass


class CommentUpdate(BaseModel):
    """更新评论模型"""
    content: str | None = Field(None, description="评论内容")
    is_visible: bool | None = Field(None, description="是否可见")


class CommentInDBBase(CommentBase):
    """数据库中的评论模型"""
    id: int
    author_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Comment(CommentInDBBase):
    """返回给API的评论模型"""
    pass


class CommentList(BaseModel):
    """评论列表模型"""
    total: int
    items: list[Comment]