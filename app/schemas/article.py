from datetime import datetime

from pydantic import BaseModel


class ArticleBase(BaseModel):
    """文章基础模型"""
    title: str
    content: str
    is_published: bool = False


class ArticleCreate(ArticleBase):
    """创建文章的请求模型"""
    pass


class ArticleUpdate(BaseModel):
    """更新文章的请求模型"""
    title: str | None = None
    content: str | None = None
    is_published: bool | None = None


class ArticleInDBBase(ArticleBase):
    """数据库中文章的基础模型"""
    id: int
    author_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Article(ArticleInDBBase):
    """API响应中的文章模型"""
    pass


class ArticleList(BaseModel):
    """文章列表响应模型"""
    total: int
    items: list[Article]