from datetime import datetime

from pydantic import BaseModel

from app.models.review import ContentType, ReviewStatus


class ReviewBase(BaseModel):
    """审核基础模型"""
    content_type: ContentType
    content_id: int
    status: ReviewStatus = ReviewStatus.PENDING
    comment: str | None = None


class ReviewCreate(ReviewBase):
    """创建审核的请求模型"""
    pass


class ReviewUpdate(BaseModel):
    """更新审核的请求模型"""
    status: ReviewStatus | None = None
    comment: str | None = None
    reviewer_id: int | None = None


class ReviewInDBBase(ReviewBase):
    """数据库中审核的基础模型"""
    id: int
    reviewer_id: int | None = None
    reviewed_at: datetime | None = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Review(ReviewInDBBase):
    """API响应中的审核模型"""
    pass


class ReviewList(BaseModel):
    """审核列表响应模型"""
    total: int
    items: list[Review]