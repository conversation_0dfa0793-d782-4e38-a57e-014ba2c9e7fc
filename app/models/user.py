from datetime import datetime

from sqlalchemy import (
    <PERSON><PERSON>an,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    Table,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import text

from app.db.session import Base

# 用户关注关系表
user_follow = Table(
    "user_follow",
    Base.metadata,
    Column(
        "follower_id",
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "followed_id",
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "created_at", DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP")
    ),
)

# 角色-权限关联表
role_permission = Table(
    "role_permission",
    Base.metadata,
    Column("role_id", Integer, ForeignKey("user_role.id"), primary_key=True),
    Column("permission_id", Integer, ForeignKey("permission.id"), primary_key=True),
)


class Permission(Base):
    """权限表"""

    __tablename__ = "permission"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, index=True, comment="权限名称")
    code = Column(String(50), unique=True, index=True, comment="权限代码")
    resource = Column(String(50), index=True, comment="资源类型")
    action = Column(String(50), comment="操作类型：read, write, delete, admin等")
    description = Column(Text, comment="权限描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    roles = relationship(
        "UserRole", secondary=role_permission, back_populates="permissions"
    )

    def __repr__(self):
        return f"<Permission {self.name}>"


class UserRole(Base):
    """用户角色"""

    __tablename__ = "user_role"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, index=True)
    desc = Column(Text)
    is_default = Column(Boolean, default=False, comment="是否为默认角色")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    permissions = relationship(
        "Permission", secondary=role_permission, back_populates="roles"
    )
    users = relationship("User", back_populates="role")

    def __repr__(self):
        return f"<UserRole {self.name}>"


class User(Base):
    """用户"""

    __tablename__ = "users"

    # 关注关系
    following = relationship(
        "User",
        secondary=user_follow,
        primaryjoin=(user_follow.c.follower_id == id),
        secondaryjoin=(user_follow.c.followed_id == id),
        backref="followers",  # 反向引用，可以通过user.followers访问关注该用户的用户列表
    )
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    password = Column(String(100))
    role_id = Column(Integer, ForeignKey("user_role.id"), index=True)
    nickname = Column(String(50))
    email = Column(String(100), unique=True, index=True, nullable=True)
    avatar = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    last_login = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    role = relationship("UserRole", back_populates="users")
    articles = relationship("Article", back_populates="author")
    videos = relationship("Video", back_populates="author")
    comments = relationship("Comment", back_populates="author")
    reviews = relationship("Review", back_populates="reviewer")

    # 关注关系
    following = relationship(
        "User",
        secondary=user_follow,
        primaryjoin=(id == user_follow.c.follower_id),
        secondaryjoin=(id == user_follow.c.followed_id),
        backref="followers",
    )

    def __repr__(self):
        return f"<User {self.username}>"

    @property
    def permissions(self):
        """获取用户所有权限"""
        if self.is_superuser:
            return ["*"]  # 超级用户拥有所有权限
        if not self.role:
            return []
        return [p.code for p in self.role.permissions if p.is_active]
