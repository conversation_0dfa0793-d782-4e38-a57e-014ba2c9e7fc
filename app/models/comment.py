import enum
from datetime import datetime

from sqlalchemy import (
    <PERSON><PERSON>an,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Integer,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.session import Base


class CommentType(str, enum.Enum):
    """评论类型枚举"""

    ARTICLE = "article"  # 文章评论
    VIDEO = "video"  # 视频评论


class Comment(Base):
    """评论数据模型"""

    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    author_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    # 评论类型
    comment_type = Column(
        Enum("article", "video", name="comment_type"), nullable=False, index=True
    )
    # 关联ID
    article_id = Column(Integer, ForeignKey("articles.id"), index=True, nullable=True)
    video_id = Column(Integer, ForeignKey("videos.id"), index=True, nullable=True)
    # 是否显示
    is_visible = Column(<PERSON><PERSON><PERSON>, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    author = relationship("User", back_populates="comments")
    article = relationship("Article", back_populates="comments")
    video = relationship("Video", back_populates="comments")

    def __repr__(self):
        return f"<Comment {self.id}>"
