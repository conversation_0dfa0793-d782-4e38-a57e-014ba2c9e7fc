from datetime import datetime

from sqlalchemy import Column, DateTime, Float, Integer, String, Text

from app.db.session import Base


class Game(Base):
    """游戏数据模型"""

    __tablename__ = "games"

    id = Column(Integer, primary_key=True, index=True)
    steam_appid = Column(Integer, unique=True, index=True, nullable=False)
    name = Column(String(255), index=True, nullable=False)
    description = Column(Text, nullable=True)
    developer = Column(String(255), nullable=True)
    publisher = Column(String(255), nullable=True)
    price = Column(Float, nullable=True)
    discount_percent = Column(Integer, nullable=True)
    release_date = Column(DateTime, nullable=True)
    image_url = Column(String(512), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    # categories = relationship("Category", secondary="game_categories", back_populates="games")
    # tags = relationship("Tag", secondary="game_tags", back_populates="games")

    def __repr__(self):
        return f"<Game {self.name}>"
