from datetime import datetime

from sqlalchemy import (
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
)
from sqlalchemy.orm import relationship

from app.db.session import Base


class Category(Base):
    """类别数据模型"""

    __tablename__ = "categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, index=True, nullable=False)
    description = Column(String(255), nullable=True)
    parent_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    parent = relationship("Category", remote_side=[id], backref="children")
    articles = relationship("Article", back_populates="category")
    videos = relationship("Video", back_populates="category")

    def __repr__(self):
        return f"<Category {self.name}>"