from fastapi import APIRouter

from app.api.endpoints import (
    articles,
    auth,
    comments,
    games,
    reviews,
    roles,
    users,
    videos,
)

api_router = APIRouter()
api_router.include_router(games.router, prefix="/games", tags=["games"])
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(roles.router, prefix="/roles", tags=["roles"])
api_router.include_router(articles.router, prefix="/articles", tags=["articles"])
api_router.include_router(videos.router, prefix="/videos", tags=["videos"])
api_router.include_router(reviews.router, prefix="/reviews", tags=["reviews"])
api_router.include_router(comments.router, prefix="/comments", tags=["comments"])
