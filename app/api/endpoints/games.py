from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.deps import check_permissions
from app.db.session import get_db
from app.models.game import Game as GameModel
from app.models.user import User
from app.schemas.game import Game, GameCreate, GameList, GameUpdate

router = APIRouter()


@router.get("/", response_model=GameList)
async def get_games(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    name: str | None = None,
    current_user: User = Depends(check_permissions(["games:list"])),
) -> Any:
    """获取游戏列表"""
    query = db.query(GameModel)
    
    # 应用过滤条件
    if name:
        query = query.filter(GameModel.name.ilike(f"%{name}%"))
    
    # 获取总数
    total = query.count()
    
    # 应用分页
    games = query.offset(skip).limit(limit).all()
    
    return {"total": total, "items": games}


@router.post("/", response_model=Game)
async def create_game(
    *,
    db: Session = Depends(get_db),
    game_in: GameCreate,
    current_user: User = Depends(check_permissions(["games:create"])),
) -> Any:
    """创建新游戏"""
    # 检查是否已存在相同的Steam AppID
    db_game = db.query(GameModel).filter(GameModel.steam_appid == game_in.steam_appid).first()
    if db_game:
        raise HTTPException(
            status_code=400,
            detail=f"Game with Steam AppID {game_in.steam_appid} already exists",
        )
    
    # 创建新游戏
    db_game = GameModel(**game_in.model_dump())
    db.add(db_game)
    db.commit()
    db.refresh(db_game)
    return db_game


@router.get("/{game_id}", response_model=Game)
async def get_game(
    *,
    db: Session = Depends(get_db),
    game_id: int,
    current_user: User = Depends(check_permissions(["games:read"])),
) -> Any:
    """获取特定游戏的详细信息"""
    game = db.query(GameModel).filter(GameModel.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")
    return game


@router.put("/{game_id}", response_model=Game)
async def update_game(
    *,
    db: Session = Depends(get_db),
    game_id: int,
    game_in: GameUpdate,
    current_user: User = Depends(check_permissions(["games:update"])),
) -> Any:
    """更新游戏信息"""
    game = db.query(GameModel).filter(GameModel.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")
    
    # 更新游戏信息
    update_data = game_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(game, field, value)
    
    db.add(game)
    db.commit()
    db.refresh(game)
    return game


@router.delete("/{game_id}", response_model=Game)
async def delete_game(
    *,
    db: Session = Depends(get_db),
    game_id: int,
    current_user: User = Depends(check_permissions(["games:delete"])),
) -> Any:
    """删除游戏"""
    game = db.query(GameModel).filter(GameModel.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")
    
    db.delete(game)
    db.commit()
    return game