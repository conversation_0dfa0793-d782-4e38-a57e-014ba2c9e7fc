# 角色和权限模型响应模式
from datetime import datetime
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.api.deps import check_permissions, get_current_active_superuser
from app.db.session import get_db
from app.models.user import Permission, UserRole

router = APIRouter()


class PermissionBase(BaseModel):
    name: str
    code: str
    resource: str
    action: str
    description: str | None = None
    is_active: bool = True


class PermissionCreate(PermissionBase):
    pass


class PermissionUpdate(BaseModel):
    name: str | None = None
    description: str | None = None
    is_active: bool | None = None


class PermissionInDB(PermissionBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


class RoleBase(BaseModel):
    name: str
    description: str | None = None
    is_default: bool = False
    is_active: bool = True


class RoleCreate(RoleBase):
    permission_ids: list[int] = []


class RoleUpdate(BaseModel):
    name: str | None = None
    description: str | None = None
    is_default: bool | None = None
    is_active: bool | None = None
    permission_ids: list[int] | None = None


class RoleInDB(RoleBase):
    id: int
    created_at: datetime
    updated_at: datetime
    permissions: list[PermissionInDB] = []

    class Config:
        orm_mode = True


# 角色管理API
@router.get("/", response_model=list[RoleInDB])
async def get_roles(
    db: Session = Depends(get_db),
    current_user=Depends(check_permissions(["roles:list"])),
) -> Any:
    """获取所有角色"""
    roles = db.query(UserRole).all()
    return roles


@router.post("/", response_model=RoleInDB, status_code=status.HTTP_201_CREATED)
async def create_role(
    *,
    db: Session = Depends(get_db),
    role_in: RoleCreate,
    current_user=Depends(check_permissions(["roles:create"])),
) -> Any:
    """创建新角色"""
    # 检查角色名是否已存在
    role = db.query(UserRole).filter(UserRole.name == role_in.name).first()
    if role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色名已存在",
        )

    # 如果设置为默认角色，需要将其他默认角色设置为非默认
    if role_in.is_default:
        db.query(UserRole).filter(UserRole.is_default == True).update(
            {"is_default": False}
        )

    # 创建角色
    role = UserRole(
        name=role_in.name,
        description=role_in.description,
        is_default=role_in.is_default,
        is_active=role_in.is_active,
    )
    db.add(role)
    db.commit()
    db.refresh(role)

    # 添加权限
    if role_in.permission_ids:
        permissions = (
            db.query(Permission).filter(Permission.id.in_(role_in.permission_ids)).all()
        )
        role.permissions = permissions
        db.commit()
        db.refresh(role)

    return role


@router.get("/{role_id}", response_model=RoleInDB)
async def get_role(
    *,
    db: Session = Depends(get_db),
    role_id: int,
    current_user=Depends(check_permissions(["roles:read"])),
) -> Any:
    """获取特定角色的详细信息"""
    role = db.query(UserRole).filter(UserRole.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在",
        )
    return role


@router.put("/{role_id}", response_model=RoleInDB)
async def update_role(
    *,
    db: Session = Depends(get_db),
    role_id: int,
    role_in: RoleUpdate,
    current_user=Depends(check_permissions(["roles:update"])),
) -> Any:
    """更新角色信息"""
    role = db.query(UserRole).filter(UserRole.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在",
        )

    # 检查角色名是否已存在
    if role_in.name and role_in.name != role.name:
        existing_role = db.query(UserRole).filter(UserRole.name == role_in.name).first()
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色名已存在",
            )

    # 如果设置为默认角色，需要将其他默认角色设置为非默认
    if (
        role_in.is_default
        and role_in.is_default != role.is_default
        and role_in.is_default == True
    ):
        db.query(UserRole).filter(UserRole.is_default == True).update(
            {"is_default": False}
        )

    # 更新角色信息
    if role_in.name is not None:
        role.name = role_in.name
    if role_in.description is not None:
        role.description = role_in.description
    if role_in.is_default is not None:
        role.is_default = role_in.is_default
    if role_in.is_active is not None:
        role.is_active = role_in.is_active

    # 更新权限
    if role_in.permission_ids is not None:
        permissions = (
            db.query(Permission).filter(Permission.id.in_(role_in.permission_ids)).all()
        )
        role.permissions = permissions

    db.commit()
    db.refresh(role)
    return role


@router.delete("/{role_id}", response_model=RoleInDB)
async def delete_role(
    *,
    db: Session = Depends(get_db),
    role_id: int,
    current_user=Depends(check_permissions(["roles:delete"])),
) -> Any:
    """删除角色"""
    role = db.query(UserRole).filter(UserRole.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在",
        )

    # 检查角色是否有关联的用户
    if role.users:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该角色下有关联的用户，无法删除",
        )

    # 删除角色
    db.delete(role)
    db.commit()
    return role


# 权限管理API
@router.get("/permissions/", response_model=list[PermissionInDB])
async def get_permissions(
    db: Session = Depends(get_db),
    current_user=Depends(check_permissions(["permissions:list"])),
) -> Any:
    """获取所有权限"""
    permissions = db.query(Permission).all()
    return permissions


@router.post(
    "/permissions/", response_model=PermissionInDB, status_code=status.HTTP_201_CREATED
)
async def create_permission(
    *,
    db: Session = Depends(get_db),
    permission_in: PermissionCreate,
    current_user=Depends(get_current_active_superuser),
) -> Any:
    """创建新权限（仅超级管理员可操作）"""
    # 检查权限代码是否已存在
    permission = (
        db.query(Permission).filter(Permission.code == permission_in.code).first()
    )
    if permission:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="权限代码已存在",
        )

    # 创建权限
    permission = Permission(
        name=permission_in.name,
        code=permission_in.code,
        resource=permission_in.resource,
        action=permission_in.action,
        description=permission_in.description,
        is_active=permission_in.is_active,
    )
    db.add(permission)
    db.commit()
    db.refresh(permission)

    return permission


@router.get("/permissions/{permission_id}", response_model=PermissionInDB)
async def get_permission(
    *,
    db: Session = Depends(get_db),
    permission_id: int,
    current_user=Depends(check_permissions(["permissions:read"])),
) -> Any:
    """获取特定权限的详细信息"""
    permission = db.query(Permission).filter(Permission.id == permission_id).first()
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在",
        )
    return permission


@router.put("/permissions/{permission_id}", response_model=PermissionInDB)
async def update_permission(
    *,
    db: Session = Depends(get_db),
    permission_id: int,
    permission_in: PermissionUpdate,
    current_user=Depends(get_current_active_superuser),
) -> Any:
    """更新权限信息（仅超级管理员可操作）"""
    permission = db.query(Permission).filter(Permission.id == permission_id).first()
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在",
        )

    # 更新权限信息
    if permission_in.name is not None:
        permission.name = permission_in.name
    if permission_in.description is not None:
        permission.description = permission_in.description
    if permission_in.is_active is not None:
        permission.is_active = permission_in.is_active

    db.commit()
    db.refresh(permission)
    return permission


@router.delete("/permissions/{permission_id}", response_model=PermissionInDB)
async def delete_permission(
    *,
    db: Session = Depends(get_db),
    permission_id: int,
    current_user=Depends(get_current_active_superuser),
) -> Any:
    """删除权限（仅超级管理员可操作）"""
    permission = db.query(Permission).filter(Permission.id == permission_id).first()
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在",
        )

    # 检查权限是否有关联的角色
    if permission.roles:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该权限已分配给角色，无法删除",
        )

    # 删除权限
    db.delete(permission)
    db.commit()
    return permission
