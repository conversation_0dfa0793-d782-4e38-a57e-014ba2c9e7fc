from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.post("/", response_model=schemas.Video)
def create_video(
    *,
    db: Session = Depends(deps.get_db),
    video_in: schemas.VideoCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """创建新视频"""
    video = crud.video.create(db=db, obj_in=video_in)
    # 创建审核记录
    review_in = schemas.ReviewCreate(
        content_type="video",
        content_id=video.id,
    )
    crud.review.create(db=db, obj_in=review_in)
    return video


@router.get("/", response_model=schemas.VideoList)
def read_videos(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取视频列表"""
    # 管理员可以看到所有视频，普通用户只能看到已发布的视频
    if crud.user.is_admin(current_user):
        videos = crud.video.get_multi(db, skip=skip, limit=limit)
        total = db.query(models.Video).count()
    else:
        videos = crud.video.get_published(db, skip=skip, limit=limit)
        total = db.query(models.Video).filter(models.Video.is_published == True).count()
    return {"total": total, "items": videos}


@router.get("/my", response_model=schemas.VideoList)
def read_my_videos(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取当前用户的视频列表"""
    videos = crud.video.get_multi_by_author(
        db=db, author_id=current_user.id, skip=skip, limit=limit
    )
    total = db.query(models.Video).filter(models.Video.author_id == current_user.id).count()
    return {"total": total, "items": videos}


@router.get("/{video_id}", response_model=schemas.Video)
def read_video(
    *,
    db: Session = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取视频详情"""
    video = crud.video.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    # 非管理员且非作者只能查看已发布的视频
    if not video.is_published and not crud.user.is_admin(current_user) and video.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该视频",
        )
    return video


@router.put("/{video_id}", response_model=schemas.Video)
def update_video(
    *,
    db: Session = Depends(deps.get_db),
    video_id: int,
    video_in: schemas.VideoUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新视频"""
    video = crud.video.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    # 只有管理员和作者可以更新视频
    if not crud.user.is_admin(current_user) and video.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新该视频",
        )
    # 如果内容有更新，需要重新审核
    if video_in.title or video_in.description or video_in.url:
        video_in_dict = video_in.dict(exclude_unset=True)
        video_in_dict["is_published"] = False
        video = crud.video.update(db=db, db_obj=video, obj_in=video_in_dict)
        # 创建新的审核记录
        review = crud.review.get_by_content(db, content_type="video", content_id=video.id)
        if review:
            crud.review.update(db=db, db_obj=review, obj_in={"status": "pending", "reviewer_id": None, "reviewed_at": None})
        else:
            review_in = schemas.ReviewCreate(
                content_type="video",
                content_id=video.id,
            )
            crud.review.create(db=db, obj_in=review_in)
    else:
        video = crud.video.update(db=db, db_obj=video, obj_in=video_in)
    return video


@router.delete("/{video_id}", response_model=schemas.Video)
def delete_video(
    *,
    db: Session = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """删除视频"""
    video = crud.video.get(db, id=video_id)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )
    # 只有管理员和作者可以删除视频
    if not crud.user.is_admin(current_user) and video.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除该视频",
        )
    # 删除关联的审核记录
    review = crud.review.get_by_content(db, content_type="video", content_id=video.id)
    if review:
        crud.review.remove(db=db, id=review.id)
    video = crud.video.remove(db=db, id=video_id)
    return video