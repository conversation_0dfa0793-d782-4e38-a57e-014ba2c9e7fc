"""微信公众号消息推送"""

import hashlib

from fastapi import APIRouter, HTTPException, Request

from app.core.config import get_settings

settings = get_settings()

router = APIRouter()


def verify_wechat_signature(signature: str, timestamp: str, nonce: str) -> bool:
    """
    验证微信签名

    Args:
        signature: 微信加密签名
        timestamp: 时间戳
        nonce: 随机数

    Returns:
        bool: 验证是否通过
    """
    # 获取微信Token
    token = settings.WECHAT_MESSAGE_TOKEN
    print("token", token)

    # 将Token、timestamp、nonce三个参数进行字典序排序
    params = [token, timestamp, nonce]
    params.sort()

    # 将三个参数字符串拼接成一个字符串进行sha1加密
    temp_str = "".join(params)
    sha1_hash = hashlib.sha1(temp_str.encode("utf-8")).hexdigest()

    # 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
    return sha1_hash == signature


@router.get("/")
async def receive_wechat_message(
    request: Request, signature: str, timestamp: str, nonce: str, echostr: str
) -> str:
    """
    接收微信公众号推送的消息

    微信服务器在向第三方服务器发起请求时，会在请求的query string中携带以下参数：
    - signature: 微信加密签名，signature结合了开发者填写的token参数和请求中的timestamp参数、nonce参数
    - timestamp: 时间戳
    - nonce: 随机数
    - echostr: 随机字符串

    开发者通过检验signature对请求进行校验（下面有校验方式）。
    若确认此次GET请求来自微信服务器，请原样返回echostr参数内容，则接入生效，成为开发者成功，否则接入失败。

    加密/校验流程如下：
    1. 将token、timestamp、nonce三个参数进行字典序排序
    2. 将三个参数字符串拼接成一个字符串进行sha1加密
    3. 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
    """
    # 验证签名
    if not verify_wechat_signature(signature, timestamp, nonce):
        raise HTTPException(status_code=403, detail="签名验证失败")

    # 验证通过，返回echostr
    return HTMLResponse(content=echostr)
