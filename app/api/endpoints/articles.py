from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.post("/", response_model=schemas.Article)
def create_article(
    *,
    db: Session = Depends(deps.get_db),
    article_in: schemas.ArticleCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """创建新文章"""
    article = crud.article.create(db=db, obj_in=article_in)
    # 创建审核记录
    review_in = schemas.ReviewCreate(
        content_type="article",
        content_id=article.id,
    )
    crud.review.create(db=db, obj_in=review_in)
    return article


@router.get("/", response_model=schemas.ArticleList)
def read_articles(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取文章列表"""
    # 管理员可以看到所有文章，普通用户只能看到已发布的文章
    if crud.user.is_admin(current_user):
        articles = crud.article.get_multi(db, skip=skip, limit=limit)
        total = db.query(models.Article).count()
    else:
        articles = crud.article.get_published(db, skip=skip, limit=limit)
        total = db.query(models.Article).filter(models.Article.is_published == True).count()
    return {"total": total, "items": articles}


@router.get("/my", response_model=schemas.ArticleList)
def read_my_articles(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取当前用户的文章列表"""
    articles = crud.article.get_multi_by_author(
        db=db, author_id=current_user.id, skip=skip, limit=limit
    )
    total = db.query(models.Article).filter(models.Article.author_id == current_user.id).count()
    return {"total": total, "items": articles}


@router.get("/{article_id}", response_model=schemas.Article)
def read_article(
    *,
    db: Session = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取文章详情"""
    article = crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 非管理员且非作者只能查看已发布的文章
    if not article.is_published and not crud.user.is_admin(current_user) and article.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该文章",
        )
    return article


@router.put("/{article_id}", response_model=schemas.Article)
def update_article(
    *,
    db: Session = Depends(deps.get_db),
    article_id: int,
    article_in: schemas.ArticleUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新文章"""
    article = crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 只有管理员和作者可以更新文章
    if not crud.user.is_admin(current_user) and article.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新该文章",
        )
    # 如果内容有更新，需要重新审核
    if article_in.title or article_in.content:
        article_in_dict = article_in.dict(exclude_unset=True)
        article_in_dict["is_published"] = False
        article = crud.article.update(db=db, db_obj=article, obj_in=article_in_dict)
        # 创建新的审核记录
        review = crud.review.get_by_content(db, content_type="article", content_id=article.id)
        if review:
            crud.review.update(db=db, db_obj=review, obj_in={"status": "pending", "reviewer_id": None, "reviewed_at": None})
        else:
            review_in = schemas.ReviewCreate(
                content_type="article",
                content_id=article.id,
            )
            crud.review.create(db=db, obj_in=review_in)
    else:
        article = crud.article.update(db=db, db_obj=article, obj_in=article_in)
    return article


@router.delete("/{article_id}", response_model=schemas.Article)
def delete_article(
    *,
    db: Session = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """删除文章"""
    article = crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 只有管理员和作者可以删除文章
    if not crud.user.is_admin(current_user) and article.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除该文章",
        )
    # 删除关联的审核记录
    review = crud.review.get_by_content(db, content_type="article", content_id=article.id)
    if review:
        crud.review.remove(db=db, id=review.id)
    article = crud.article.remove(db=db, id=article_id)
    return article