from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.models.comment import CommentType

router = APIRouter()


@router.post("/", response_model=schemas.Comment, status_code=status.HTTP_201_CREATED)
def create_comment(
    *,
    db: Session = Depends(deps.get_db),
    comment_in: schemas.CommentCreate,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """创建评论"""
    # 验证评论类型和关联ID
    if comment_in.comment_type == CommentType.ARTICLE:
        if not comment_in.article_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文章评论必须提供article_id",
            )
        # 检查文章是否存在且已发布
        article = crud.article.get(db, id=comment_in.article_id)
        if not article or not article.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在或未发布",
            )
    elif comment_in.comment_type == CommentType.VIDEO:
        if not comment_in.video_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="视频评论必须提供video_id",
            )
        # 检查视频是否存在且已发布
        video = crud.video.get(db, id=comment_in.video_id)
        if not video or not video.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在或未发布",
            )

    # 创建评论
    comment = crud.comment.create(db, obj_in=comment_in, author_id=current_user.id)
    return comment


@router.get("/article/{article_id}", response_model=schemas.CommentList)
def get_article_comments(
    *,
    db: Session = Depends(deps.get_db),
    article_id: int,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """获取文章评论列表"""
    # 检查文章是否存在且已发布
    article = crud.article.get(db, id=article_id)
    if not article or not article.is_published:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在或未发布",
        )

    # 获取评论列表
    comments = crud.comment.get_by_article(
        db, article_id=article_id, skip=skip, limit=limit
    )
    total = crud.comment.count_by_article(db, article_id=article_id)

    return {"total": total, "items": comments}


@router.get("/video/{video_id}", response_model=schemas.CommentList)
def get_video_comments(
    *,
    db: Session = Depends(deps.get_db),
    video_id: int,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """获取视频评论列表"""
    # 检查视频是否存在且已发布
    video = crud.video.get(db, id=video_id)
    if not video or not video.is_published:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在或未发布",
        )

    # 获取评论列表
    comments = crud.comment.get_by_video(db, video_id=video_id, skip=skip, limit=limit)
    total = crud.comment.count_by_video(db, video_id=video_id)

    return {"total": total, "items": comments}


@router.get("/user/me", response_model=list[schemas.Comment])
def get_my_comments(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """获取当前用户的评论列表"""
    comments = crud.comment.get_by_user(
        db, author_id=current_user.id, skip=skip, limit=limit
    )
    return comments


@router.put("/{comment_id}", response_model=schemas.Comment)
def update_comment(
    *,
    db: Session = Depends(deps.get_db),
    comment_id: int,
    comment_in: schemas.CommentUpdate,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """更新评论"""
    # 获取评论
    comment = crud.comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 检查权限
    if comment.author_id != current_user.id and not crud.user.is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新此评论",
        )

    # 更新评论
    comment = crud.comment.update(db, db_obj=comment, obj_in=comment_in)
    return comment


@router.delete("/{comment_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_comment(
    *,
    db: Session = Depends(deps.get_db),
    comment_id: int,
    current_user: models.User = Depends(deps.get_current_user),
) -> None:
    """删除评论"""
    # 获取评论
    comment = crud.comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 检查权限
    if comment.author_id != current_user.id and not crud.user.is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此评论",
        )

    # 删除评论
    crud.comment.remove(db, id=comment_id)
