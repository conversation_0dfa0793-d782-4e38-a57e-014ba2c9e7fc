"""Redis连接模块"""

import logging
from typing import Any

import redis
from redis import Redis

from app.core.config import settings

logger = logging.getLogger(__name__)

# 创建Redis连接池
redis_client: Redis | None = None


def get_redis() -> Redis:
    """获取Redis客户端实例
    
    Returns:
        Redis: Redis客户端实例
    """
    global redis_client
    if redis_client is None:
        try:
            redis_client = redis.from_url(settings.REDIS_URL)
            logger.info("Redis连接已建立")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            raise
    return redis_client


def set_key(key: str, value: Any, expire: int = 0) -> bool:
    """设置Redis键值
    
    Args:
        key: 键名
        value: 值
        expire: 过期时间(秒)，0表示永不过期
        
    Returns:
        bool: 操作是否成功
    """
    try:
        client = get_redis()
        client.set(key, value)
        if expire > 0:
            client.expire(key, expire)
        return True
    except Exception as e:
        logger.error(f"设置Redis键值失败: {e}")
        return False


def get_key(key: str) -> Any:
    """获取Redis键值
    
    Args:
        key: 键名
        
    Returns:
        Any: 键值，如果不存在则返回None
    """
    try:
        client = get_redis()
        return client.get(key)
    except Exception as e:
        logger.error(f"获取Redis键值失败: {e}")
        return None


def delete_key(key: str) -> bool:
    """删除Redis键
    
    Args:
        key: 键名
        
    Returns:
        bool: 操作是否成功
    """
    try:
        client = get_redis()
        client.delete(key)
        return True
    except Exception as e:
        logger.error(f"删除Redis键失败: {e}")
        return False