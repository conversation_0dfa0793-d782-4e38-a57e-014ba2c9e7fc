from sqlalchemy.orm import Session

from app.models.user import Permission, User, UserRole


def init_permissions(db: Session) -> None:
    """初始化权限数据"""
    # 检查是否已经初始化
    if db.query(Permission).count() > 0:
        return

    # 创建基本权限
    permissions = [
        # 游戏资源权限
        Permission(
            name="查看游戏列表",
            code="games:list",
            resource="games",
            action="read",
            description="允许查看游戏列表",
        ),
        Permission(
            name="查看游戏详情",
            code="games:read",
            resource="games",
            action="read",
            description="允许查看游戏详情",
        ),
        Permission(
            name="创建游戏",
            code="games:create",
            resource="games",
            action="write",
            description="允许创建新游戏",
        ),
        Permission(
            name="更新游戏",
            code="games:update",
            resource="games",
            action="write",
            description="允许更新游戏信息",
        ),
        Permission(
            name="删除游戏",
            code="games:delete",
            resource="games",
            action="delete",
            description="允许删除游戏",
        ),
        # 用户资源权限
        Permission(
            name="查看用户列表",
            code="users:list",
            resource="users",
            action="read",
            description="允许查看用户列表",
        ),
        Permission(
            name="查看用户详情",
            code="users:read",
            resource="users",
            action="read",
            description="允许查看用户详情",
        ),
        Permission(
            name="创建用户",
            code="users:create",
            resource="users",
            action="write",
            description="允许创建新用户",
        ),
        Permission(
            name="更新用户",
            code="users:update",
            resource="users",
            action="write",
            description="允许更新用户信息",
        ),
        Permission(
            name="删除用户",
            code="users:delete",
            resource="users",
            action="delete",
            description="允许删除用户",
        ),
        # 角色资源权限
        Permission(
            name="查看角色列表",
            code="roles:list",
            resource="roles",
            action="read",
            description="允许查看角色列表",
        ),
        Permission(
            name="查看角色详情",
            code="roles:read",
            resource="roles",
            action="read",
            description="允许查看角色详情",
        ),
        Permission(
            name="创建角色",
            code="roles:create",
            resource="roles",
            action="write",
            description="允许创建新角色",
        ),
        Permission(
            name="更新角色",
            code="roles:update",
            resource="roles",
            action="write",
            description="允许更新角色信息",
        ),
        Permission(
            name="删除角色",
            code="roles:delete",
            resource="roles",
            action="delete",
            description="允许删除角色",
        ),
        # 权限资源权限
        Permission(
            name="查看权限列表",
            code="permissions:list",
            resource="permissions",
            action="read",
            description="允许查看权限列表",
        ),
        Permission(
            name="分配权限",
            code="permissions:assign",
            resource="permissions",
            action="write",
            description="允许分配权限给角色",
        ),
    ]

    db.add_all(permissions)
    db.commit()

    # 创建基本角色
    admin_role = UserRole(
        name="管理员",
        desc="系统管理员，拥有所有权限",
        is_active=True,
    )

    user_role = UserRole(
        name="普通用户",
        desc="普通用户，拥有基本查看权限",
        is_default=True,
        is_active=True,
    )

    guest_role = UserRole(
        name="访客",
        desc="访客用户，只有有限的查看权限",
        is_active=True,
    )

    db.add_all([admin_role, user_role, guest_role])
    db.commit()

    # 分配权限给角色
    # 管理员拥有所有权限
    admin_role.permissions = permissions

    # 普通用户拥有基本查看和操作权限，但不能管理用户和权限
    user_permissions = [
        p
        for p in permissions
        if p.resource == "games"
        or (p.resource == "users" and p.action == "read")
        or p.code == "users:update"  # 允许更新自己的信息
    ]
    user_role.permissions = user_permissions

    # 访客只有查看权限
    guest_permissions = [
        p for p in permissions if p.action == "read" and p.resource in ["games"]
    ]
    guest_role.permissions = guest_permissions

    db.commit()

    # 创建超级管理员用户（如果不存在）
    if not db.query(User).filter(User.username == "admin").first():
        admin_user = User(
            username="admin",
            password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            nickname="超级管理员",
            email="<EMAIL>",
            role_id=admin_role.id,
            is_active=True,
            is_superuser=True,
        )
        db.add(admin_user)
        db.commit()
