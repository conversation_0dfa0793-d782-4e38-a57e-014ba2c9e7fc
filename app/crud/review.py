from datetime import datetime
from typing import Any

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.review import ContentType, Review, ReviewStatus
from app.schemas.review import ReviewCreate, ReviewUpdate


class CRUDReview(CRUDBase[Review, ReviewCreate, ReviewUpdate]):
    def get_by_content(self, db: Session, *, content_type: ContentType, content_id: int) -> Review | None:
        """根据内容类型和内容ID获取审核记录"""
        return db.query(self.model).filter(
            self.model.content_type == content_type,
            self.model.content_id == content_id
        ).first()
    
    def get_multi_with_filter(
        self, 
        db: Session, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        content_type: ContentType | None = None,
        status: ReviewStatus | None = None,
    ) -> tuple[list[Review], int]:
        """获取审核列表，支持按内容类型和状态筛选"""
        query = db.query(self.model)
        
        if content_type:
            query = query.filter(self.model.content_type == content_type)
        
        if status:
            query = query.filter(self.model.status == status)
        
        total = query.count()
        items = query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()
        
        return items, total
    
    def update(
        self, db: Session, *, db_obj: Review, obj_in: ReviewUpdate | dict[str, Any]
    ) -> Review:
        """更新审核记录，如果状态变更为非待审核状态，则设置审核时间"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        
        # 如果状态从待审核变为其他状态，设置审核时间
        if (
            db_obj.status == ReviewStatus.PENDING and 
            "status" in update_data and 
            update_data["status"] != ReviewStatus.PENDING
        ):
            update_data["reviewed_at"] = datetime.utcnow()
        
        return super().update(db, db_obj=db_obj, obj_in=update_data)


review = CRUDReview(Review)