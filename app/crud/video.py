
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.video import Video
from app.schemas.video import VideoCreate, VideoUpdate


class CRUDVideo(CRUDBase[Video, VideoCreate, VideoUpdate]):
    def get_by_title(self, db: Session, *, title: str) -> Video | None:
        """根据标题获取视频"""
        return db.query(self.model).filter(self.model.title == title).first()
    
    def get_multi_by_author(
        self, db: Session, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的视频列表"""
        return (
            db.query(self.model)
            .filter(self.model.author_id == author_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_published(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取已发布的视频列表"""
        return (
            db.query(self.model)
            .filter(self.model.is_published == True)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def update_publish_status(
        self, db: Session, *, db_obj: Video, is_published: bool
    ) -> Video:
        """更新视频发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


video = CRUDVideo(Video)