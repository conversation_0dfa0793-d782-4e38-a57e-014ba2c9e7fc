from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.comment import Comment, CommentType
from app.schemas.comment import CommentCreate, CommentUpdate


class CRUDComment(CRUDBase[Comment, CommentCreate, CommentUpdate]):
    def get_by_article(
        self, db: Session, *, article_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取文章的评论列表"""
        return (
            db.query(self.model)
            .filter(
                self.model.comment_type == CommentType.ARTICLE,
                self.model.article_id == article_id,
                self.model.is_visible,
            )
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_video(
        self, db: Session, *, video_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取视频的评论列表"""
        return (
            db.query(self.model)
            .filter(
                self.model.comment_type == CommentType.VIDEO,
                self.model.video_id == video_id,
                self.model.is_visible,
            )
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_user(
        self, db: Session, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取用户的评论列表"""
        return (
            db.query(self.model)
            .filter(self.model.author_id == author_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def count_by_article(self, db: Session, *, article_id: int) -> int:
        """统计文章的评论数量"""
        return (
            db.query(self.model)
            .filter(
                self.model.comment_type == CommentType.ARTICLE,
                self.model.article_id == article_id,
                self.model.is_visible,
            )
            .count()
        )

    def count_by_video(self, db: Session, *, video_id: int) -> int:
        """统计视频的评论数量"""
        return (
            db.query(self.model)
            .filter(
                self.model.comment_type == CommentType.VIDEO,
                self.model.video_id == video_id,
                self.model.is_visible,
            )
            .count()
        )


comment = CRUDComment(Comment)
