from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.article import Article
from app.schemas.article import ArticleCreate, ArticleUpdate


class CRUDArticle(CRUDBase[Article, ArticleCreate, ArticleUpdate]):
    def get_by_title(self, db: Session, *, title: str) -> Article | None:
        """根据标题获取文章"""
        return db.query(self.model).filter(self.model.title == title).first()

    def get_multi_by_author(
        self, db: Session, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取指定作者的文章列表"""
        return (
            db.query(self.model)
            .filter(self.model.author_id == author_id)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_published(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取已发布的文章列表"""
        return (
            db.query(self.model)
            .filter(self.model.is_published)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_publish_status(
        self, db: Session, *, db_obj: Article, is_published: bool
    ) -> Article:
        """更新文章发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


article = CRUDArticle(Article)
