import logging
from logging.handlers import RotatingFileHandler
from pathlib import Path

# 确保日志目录存在
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

# 配置日志
logger = logging.getLogger("SteamAggregation")
logger.setLevel(logging.INFO)

# 创建处理器
file_handler = RotatingFileHandler(
    filename=log_dir / "app.log",
    maxBytes=10485760,  # 10MB
    backupCount=5,
)
stream_handler = logging.StreamHandler()

# 设置日志格式
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
file_handler.setFormatter(formatter)
stream_handler.setFormatter(formatter)

# 添加处理器到日志器
logger.addHandler(file_handler)
logger.addHandler(stream_handler)


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器

    Args:
        name (str): 日志器的名称

    Returns:
        logging.Logger: 配置好的日志器
    """
    return logging.getLogger(name)
