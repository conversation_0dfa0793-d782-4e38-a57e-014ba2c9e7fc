from typing import Any

from sqlalchemy.orm import Session

from app.models.game import Game
from app.schemas.game import GameCreate, GameUpdate


class GameService:
    """游戏服务类，处理游戏数据的业务逻辑"""

    @staticmethod
    def get_games(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        filters: dict[str, Any] | None = None,
    ) -> list[Game]:
        """获取游戏列表"""
        query = db.query(Game)

        # 应用过滤条件
        if filters:
            if "name" in filters and filters["name"]:
                query = query.filter(Game.name.ilike(f"%{filters['name']}%"))

            if "developer" in filters and filters["developer"]:
                query = query.filter(Game.developer.ilike(f"%{filters['developer']}%"))

            if "publisher" in filters and filters["publisher"]:
                query = query.filter(Game.publisher.ilike(f"%{filters['publisher']}%"))

        # 应用分页
        return query.offset(skip).limit(limit).all()

    @staticmethod
    def get_game_by_id(db: Session, game_id: int) -> Game | None:
        """通过ID获取游戏"""
        return db.query(Game).filter(Game.id == game_id).first()

    @staticmethod
    def get_game_by_steam_appid(db: Session, steam_appid: int) -> Game | None:
        """通过Steam AppID获取游戏"""
        return db.query(Game).filter(Game.steam_appid == steam_appid).first()

    @staticmethod
    def create_game(db: Session, game_in: GameCreate) -> Game:
        """创建新游戏"""
        db_game = Game(**game_in.model_dump())
        db.add(db_game)
        db.commit()
        db.refresh(db_game)
        return db_game

    @staticmethod
    def update_game(db: Session, game: Game, game_in: GameUpdate) -> Game:
        """更新游戏信息"""
        update_data = game_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(game, field, value)

        db.add(game)
        db.commit()
        db.refresh(game)
        return game

    @staticmethod
    def delete_game(db: Session, game: Game) -> Game:
        """删除游戏"""
        db.delete(game)
        db.commit()
        return game

    @staticmethod
    def count_games(db: Session, filters: dict[str, Any] | None = None) -> int:
        """计算游戏总数"""
        query = db.query(Game)

        # 应用过滤条件
        if filters:
            if "name" in filters and filters["name"]:
                query = query.filter(Game.name.ilike(f"%{filters['name']}%"))

            if "developer" in filters and filters["developer"]:
                query = query.filter(Game.developer.ilike(f"%{filters['developer']}%"))

            if "publisher" in filters and filters["publisher"]:
                query = query.filter(Game.publisher.ilike(f"%{filters['publisher']}%"))

        return query.count()
