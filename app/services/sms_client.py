"""阿里云发送短信服务"""

from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient

from app.schemas.sms import SMSIn
from app.services.logger import get_logger

logger = get_logger(__name__)


class SmsSendError(Exception):
    """自定义异常类，用于处理短信发送过程中的错误"""

    def __init__(self, message: str, recommend: str = ""):
        self.message = message
        self.recommend = recommend
        super().__init__(self.message)


class SmsClient:
    @staticmethod
    def create_client() -> Dysmsapi20170525Client:
        """
        使用凭据初始化账号Client
        @return: Client
        @throws Exception
        """
        # 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378659.html。
        credential = CredentialClient()
        config = open_api_models.Config(credential=credential)
        # Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
        config.endpoint = "dysmsapi.aliyuncs.com"
        return Dysmsapi20170525Client(config)

    @staticmethod
    def send_sms(
        sms_in: SMSIn,
    ) -> None:
        data = sms_in.model_dump()
        client = SmsClient.create_client()
        send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
            phone_numbers=data["phone_numbers"],
            sign_name=data["sign_name"],
            template_code=data["template_code"],
            template_param=data["template_param"],
        )
        runtime = util_models.RuntimeOptions()
        try:
            resp = client.send_sms_with_options(send_sms_request, runtime)
            logger.info("短信发送成功: %s", UtilClient.to_jsonstring(resp))
            ConsoleClient.log(UtilClient.to_jsonstring(resp))
        except Exception as error:
            logger.error("短信发送失败: %s", str(error))
            if hasattr(error, "data") and error.data.get("Recommend"):
                logger.error("诊断地址: %s", error.data.get("Recommend"))
            raise SmsSendError(
                str(error),
                error.data.get("Recommend", "") if hasattr(error, "data") else "",
            ) from error
