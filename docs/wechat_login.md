# 微信公众号扫码登录功能

## 功能概述

本系统实现了完整的微信公众号扫码登录功能，用户可以通过扫描二维码快速登录系统，无需输入用户名和密码。

## 实现步骤

### 1. Token、timestamp、nonce 字典序排序

在微信消息验证中，需要对三个参数进行字典序排序：

```python
def verify_wechat_signature(signature: str, timestamp: str, nonce: str) -> bool:
    # 获取微信Token
    token = settings.WECHAT_MESSAGE_TOKEN
    
    # 将Token、timestamp、nonce三个参数进行字典序排序
    params = [token, timestamp, nonce]
    params.sort()  # 字典序排序
    
    # 将三个参数字符串拼接成一个字符串进行sha1加密
    temp_str = "".join(params)
    sha1_hash = hashlib.sha1(temp_str.encode("utf-8")).hexdigest()
    
    # 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
    return sha1_hash == signature
```

### 2. 系统架构

```
前端页面 → FastAPI后端 → 微信API → Redis缓存
    ↓           ↓           ↓         ↓
  显示二维码   生成二维码   创建临时码   存储状态
    ↓           ↓           ↓         ↓
  轮询状态    检查状态     推送事件    更新状态
    ↓           ↓           ↓         ↓
  完成登录    返回Token    用户确认    生成Token
```

### 3. 核心组件

#### 3.1 微信服务类 (`app/services/wechat_service.py`)

- `WeChatService`: 处理微信API调用
  - `get_access_token()`: 获取微信access_token
  - `create_qr_code()`: 创建临时二维码
  - `get_user_info()`: 获取用户基本信息

- `QRCodeLoginService`: 处理扫码登录逻辑
  - `create_login_qr_code()`: 创建登录二维码
  - `get_login_status()`: 获取登录状态
  - `update_login_status()`: 更新登录状态

#### 3.2 API端点 (`app/api/endpoints/wechat.py`)

- `POST /wechat/qr-login/create`: 创建登录二维码
- `GET /wechat/qr-login/status/{scene_str}`: 获取登录状态
- `POST /wechat/register-with-wechat`: 微信用户注册
- `GET /wechat/`: 微信消息验证
- `POST /wechat/`: 处理微信事件消息

#### 3.3 数据模型扩展

用户表新增微信相关字段：
```sql
ALTER TABLE users ADD COLUMN wechat_openid VARCHAR(100) UNIQUE;
ALTER TABLE users ADD COLUMN wechat_unionid VARCHAR(100) UNIQUE;
ALTER TABLE users ADD COLUMN wechat_nickname VARCHAR(100);
ALTER TABLE users ADD COLUMN wechat_avatar VARCHAR(255);
ALTER TABLE users ADD COLUMN login_type VARCHAR(20) DEFAULT 'password';
ALTER TABLE users ALTER COLUMN password DROP NOT NULL;
```

### 4. 登录流程

#### 4.1 生成二维码
1. 前端调用 `POST /wechat/qr-login/create`
2. 后端生成唯一场景字符串 `login_{uuid}_{timestamp}`
3. 调用微信API创建临时二维码
4. 在Redis中存储场景信息（状态：waiting）
5. 返回二维码URL给前端

#### 4.2 用户扫码
1. 用户使用微信扫描二维码
2. 微信服务器推送SCAN事件到后端
3. 后端解析事件，获取openid和场景字符串
4. 获取用户微信信息
5. 更新Redis中的登录状态（状态：confirmed）

#### 4.3 前端轮询
1. 前端每2秒调用 `GET /wechat/qr-login/status/{scene_str}`
2. 后端检查Redis中的状态
3. 根据状态返回相应信息：
   - `waiting`: 等待扫码
   - `scanned`: 已扫码
   - `confirmed`: 确认登录
   - `expired`: 二维码过期
   - `need_register`: 需要注册

#### 4.4 登录或注册
- 如果用户已存在：生成JWT token，返回登录成功
- 如果用户不存在：返回需要注册状态，前端显示注册表单

### 5. 配置要求

在 `.env` 文件中配置微信相关参数：

```env
# 微信公众号设置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret
WECHAT_MESSAGE_TOKEN=your-wechat-message-token

# Redis设置（用于存储登录状态）
REDIS_URL=redis://localhost:6379/0
```

### 6. 使用示例

#### 6.1 启动服务
```bash
# 安装依赖
pip install -r requirements.txt

# 运行数据库迁移
alembic upgrade head

# 启动服务
python main.py
```

#### 6.2 访问演示页面
打开浏览器访问：`http://localhost:8000/static/wechat_login.html`

#### 6.3 API调用示例

创建二维码：
```bash
curl -X POST http://localhost:8000/wechat/qr-login/create
```

检查登录状态：
```bash
curl http://localhost:8000/wechat/qr-login/status/login_abc123_1234567890
```

### 7. 安全考虑

1. **签名验证**: 所有微信消息都进行签名验证
2. **时效性**: 二维码10分钟过期
3. **唯一性**: 每个二维码只能使用一次
4. **状态管理**: 使用Redis管理登录状态，防止重复登录

### 8. 错误处理

- 微信API调用失败：返回具体错误信息
- 二维码过期：自动清理Redis数据
- 用户信息获取失败：仍允许登录，但信息不完整
- 网络异常：前端显示重试选项

### 9. 扩展功能

- 支持微信UnionID，实现多应用账号统一
- 添加用户微信信息同步功能
- 实现微信消息推送功能
- 支持微信支付集成

## 总结

本实现提供了完整的微信公众号扫码登录解决方案，包括：
- 完整的后端API
- 前端演示页面
- 数据库迁移脚本
- 详细的文档说明

通过字典序排序确保了微信消息验证的正确性，通过Redis缓存实现了高效的状态管理，为用户提供了便捷的登录体验。
