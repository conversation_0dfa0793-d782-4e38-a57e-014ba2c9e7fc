from fastapi.testclient import TestClient

from app.main import app

# 创建测试客户端
client = TestClient(app)


def test_read_main():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "欢迎使用Steam游戏数据聚合API"}


def test_health_check():
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}


def test_get_games():
    """测试获取游戏列表"""
    response = client.get("/api/v1/games/")
    assert response.status_code == 200
    data = response.json()
    assert "total" in data
    assert "items" in data
    assert isinstance(data["items"], list)