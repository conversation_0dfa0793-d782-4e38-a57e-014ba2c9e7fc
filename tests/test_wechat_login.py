"""测试微信扫码登录功能"""

from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)


class TestWeChatLogin:
    """微信扫码登录测试"""

    def test_wechat_signature_verification(self):
        """测试微信签名验证"""
        from app.api.endpoints.wechat import verify_wechat_signature

        # 模拟微信签名验证
        with patch("app.api.endpoints.wechat.settings") as mock_settings:
            mock_settings.WECHAT_MESSAGE_TOKEN = "test_token"

            # 测试正确的签名
            signature = "da39a3ee5e6b4b0d3255bfef95601890afd80709"  # sha1("")
            timestamp = ""
            nonce = ""

            # 这里需要根据实际的token计算正确的签名
            # 暂时跳过具体验证逻辑测试
            assert callable(verify_wechat_signature)

    @patch("app.services.wechat_service.qr_login_service.create_login_qr_code")
    def test_create_qr_login(self, mock_create_qr):
        """测试创建登录二维码"""
        # 模拟返回数据
        mock_create_qr.return_value = {
            "scene_str": "login_test_123",
            "qr_url": "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=test_ticket",
            "expire_seconds": 600,
        }

        response = client.post("/wechat/qr-login/create")

        assert response.status_code == 200
        data = response.json()
        assert "scene_str" in data
        assert "qr_url" in data
        assert "expire_seconds" in data

    @patch("app.services.wechat_service.qr_login_service.get_login_status")
    def test_get_login_status_waiting(self, mock_get_status):
        """测试获取登录状态 - 等待状态"""
        mock_get_status.return_value = {"status": "waiting", "message": "等待扫码"}

        response = client.get("/wechat/qr-login/status/test_scene")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "waiting"

    @patch("app.services.wechat_service.qr_login_service.get_login_status")
    def test_get_login_status_expired(self, mock_get_status):
        """测试获取登录状态 - 过期状态"""
        mock_get_status.return_value = {"status": "expired", "message": "二维码已过期"}

        response = client.get("/wechat/qr-login/status/test_scene")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "expired"

    def test_wechat_message_verification_get(self):
        """测试微信消息验证 - GET请求"""
        # 这里需要计算正确的签名，暂时测试接口存在性
        response = client.get(
            "/wechat/",
            params={
                "signature": "test_signature",
                "timestamp": "1234567890",
                "nonce": "test_nonce",
                "echostr": "test_echo",
            },
        )

        # 由于签名验证会失败，期望返回403
        assert response.status_code == 403

    def test_parse_wechat_xml(self):
        """测试微信XML消息解析"""
        from app.api.endpoints.wechat import parse_wechat_xml

        xml_data = """
        <xml>
            <ToUserName><![CDATA[toUser]]></ToUserName>
            <FromUserName><![CDATA[fromUser]]></FromUserName>
            <CreateTime>123456789</CreateTime>
            <MsgType><![CDATA[event]]></MsgType>
            <Event><![CDATA[SCAN]]></Event>
            <EventKey><![CDATA[login_test_123]]></EventKey>
        </xml>
        """

        result = parse_wechat_xml(xml_data)

        assert result["ToUserName"] == "toUser"
        assert result["FromUserName"] == "fromUser"
        assert result["MsgType"] == "event"
        assert result["Event"] == "SCAN"
        assert result["EventKey"] == "login_test_123"

    def test_parse_invalid_xml(self):
        """测试解析无效XML"""
        from app.api.endpoints.wechat import parse_wechat_xml

        invalid_xml = "not xml content"
        result = parse_wechat_xml(invalid_xml)

        assert result == {}


class TestWeChatService:
    """微信服务测试"""

    @patch("httpx.AsyncClient")
    def test_get_access_token_from_api(self, mock_client):
        """测试从API获取access_token"""
        from app.services.wechat_service import WeChatService

        # 模拟HTTP响应
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "access_token": "test_access_token",
            "expires_in": 7200,
        }

        mock_client_instance = MagicMock()
        mock_client_instance.get.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance

        service = WeChatService()

        with patch("app.services.wechat_service.redis_client") as mock_redis:
            mock_redis.get.return_value = None  # 缓存中没有token

            # 由于这是异步方法，我们只测试方法存在性
            assert hasattr(service, "get_access_token")
            assert callable(service.get_access_token)

    def test_generate_login_scene(self):
        """测试生成登录场景字符串"""
        from app.services.wechat_service import QRCodeLoginService

        service = QRCodeLoginService()
        scene_str = service.generate_login_scene()

        assert scene_str.startswith("login_")
        assert len(scene_str.split("_")) == 3  # login_uuid_timestamp


class TestUserModel:
    """用户模型测试"""

    def test_user_model_has_wechat_fields(self):
        """测试用户模型包含微信字段"""
        from app.models.user import User

        # 检查模型是否有微信相关字段
        assert hasattr(User, "wechat_openid")
        assert hasattr(User, "wechat_unionid")
        assert hasattr(User, "wechat_nickname")
        assert hasattr(User, "wechat_avatar")
        assert hasattr(User, "login_type")


if __name__ == "__main__":
    pytest.main([__file__])
