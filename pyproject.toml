[project]
name = "steam-aggregation-backend"
version = "0.1.0"
description = "Steam游戏数据聚合后端服务"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "ruff>=0.12.0",
    "fastapi>=0.115.0",
    "uvicorn>=0.33.0",
    "pydantic>=2.10.0",
    "sqlalchemy>=2.0.0",
    "python-dotenv>=1.0.0",
    "psycopg2-binary>=2.9.10",
    "alembic>=1.16.2",
    "celery>=5.5.3",
    "python-multipart>=0.0.20",
]

[project.optional-dependencies]
dev = ["ruff"]

[tool.ruff]
line-length = 88
target-version = "py312"
indent-width = 4
# 文件选择
respect-gitignore = true

[tool.ruff.lint]
# 启用的规则
select = [
    "E",  # pycodestyle 错误
    "F",  # pyflakes
    "I",  # isort
    "UP", # pyupgrade
    "N",  # pep8-naming
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
    "TID", # flake8-tidy-imports
    "Q",  # flake8-quotes
]

# 忽略的规则
ignore = []

# 自动修复
fixable = ["ALL"]
unfixable = []

# 格式化
[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"
skip-magic-trailing-comma = false
docstring-code-format = false
docstring-code-line-length = "dynamic"

[tool.ruff.lint.isort]
known-first-party = ["steam_aggregation_backend"]

[tool.ruff.lint.pep8-naming]
classmethod-decorators = [
    "classmethod",
]
